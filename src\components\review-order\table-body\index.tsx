import React from "react";
import styles from "./index.module.scss";
import { useHistory } from "react-router";
import { setLastLocation } from "../../../utils/setLastLocation";

interface ITabeBodyProps {
  getTableProps: () => { [key: string]: any };
  headerGroups: Array<any>;
  getTableBodyProps: () => { [key: string]: any };
  page: Array<any>;
  prepareRow: (arg: any) => void;
}

export default function TableBody({
  getTableProps,
  headerGroups,
  getTableBodyProps,
  page,
  prepareRow,
}: ITabeBodyProps) {
  const history = useHistory();
  return (
    <div className={`table-responsive ${styles.tableWrapper}`}>
      <table
        className={`table table-borderless  ${styles.tableInnerWrapper}`}
        {...getTableProps()}
      >
        <thead className={`${styles.tableHeader} mb-3`}>
          {headerGroups.map((headerGroup) => (
            <tr {...headerGroup.getHeaderGroupProps()} className={styles.rows}>
              {headerGroup.headers.map((column: any) => (
                <td {...column.getHeaderProps()} className={styles.tableTh}>
                  {column.render("Header")}
                </td>
              ))}
            </tr>
          ))}
        </thead>

        <tbody {...getTableBodyProps()} className={`${styles.tableBody}`}>
          {page.map((row) => {
            prepareRow(row);
            return (
              <tr
                {...row.getRowProps()}
                onClick={() => {
                  setLastLocation("Order Summary");
                  history.push(`/deal-tracker/${row?.original?.id}`);
                }}
              >
                {row.cells.map((cell: any) => (
                  <td {...cell.getCellProps()} className={styles.tableRow}>
                    {cell.render("Cell")}
                  </td>
                ))}
              </tr>
            );
          })}
        </tbody>
      </table>
    </div>
  );
}
