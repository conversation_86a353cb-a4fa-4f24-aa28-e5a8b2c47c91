import React from 'react';
import { useParams, useLocation, useHistory } from 'react-router-dom';

/**
 * Debug component to help troubleshoot routing issues
 */
const RouteDebug: React.FC = () => {
  const params = useParams();
  const location = useLocation();
  const history = useHistory();

  React.useEffect(() => {
    console.log('=== ROUTE DEBUG ===');
    console.log('Current location:', location);
    console.log('URL params:', params);
    console.log('History:', history);
    console.log('==================');
  }, [location, params, history]);

  return (
    <div style={{ 
      position: 'fixed', 
      top: 10, 
      right: 10, 
      background: 'rgba(0,0,0,0.8)', 
      color: 'white', 
      padding: '10px', 
      borderRadius: '5px',
      fontSize: '12px',
      zIndex: 9999,
      maxWidth: '300px'
    }}>
      <div><strong>Route Debug</strong></div>
      <div>Path: {location.pathname}</div>
      <div>Hash: {location.hash}</div>
      <div>Search: {location.search}</div>
      <div>Params: {JSON.stringify(params)}</div>
    </div>
  );
};

export default RouteDebug;
