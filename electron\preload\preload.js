const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  // App information
  getVersion: () => ipcRenderer.invoke('app-version'),
  
  // Dialog methods
  showMessageBox: (options) => ipcRenderer.invoke('show-message-box', options),
  
  // Menu events (listen only)
  onMenuNew: (callback) => ipcRenderer.on('menu-new', callback),
  onMenuOpen: (callback) => ipcRenderer.on('menu-open', callback),
  onMenuSave: (callback) => ipcRenderer.on('menu-save', callback),
  
  // Database operations (will be added later)
  database: {
    // Placeholder for database operations
  },
  
  // Network status
  onOnline: (callback) => {
    window.addEventListener('online', callback);
    return () => window.removeEventListener('online', callback);
  },
  
  onOffline: (callback) => {
    window.addEventListener('offline', callback);
    return () => window.removeEventListener('offline', callback);
  },
  
  // File system operations (will be added later)
  fileSystem: {
    // Placeholder for file system operations
  },
  
  // Authentication (will be added later)
  auth: {
    // Placeholder for authentication operations
  },
  
  // Sync operations (will be added later)
  sync: {
    // Placeholder for sync operations
  }
});

// Security: Remove access to Node.js APIs in the renderer process
delete window.require;
delete window.exports;
delete window.module;
