{"version": 3, "file": "main.js", "mappings": ";;;;;;;;;;AAAA;;;;;;;;;;;ACAA;;;;;;;;;;;ACAA;;;;;;;;;;;ACAA;;;;;;;;;;;ACAA;;;;;;;;;;;ACAA;;;;;;UCAA;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA;UACA;;UAEA;UACA;UACA;;;;;;;;ACtBA,MAAM;EAAEA,GAAG;EAAEC,aAAa;EAAEC,IAAI;EAAEC,KAAK;EAAEC,OAAO;EAAEC;AAAO,CAAC,GAAGC,mBAAO,CAAC,0BAAU,CAAC;AAChF,MAAM;EAAEC,IAAI;EAAEC;AAAQ,CAAC,GAAGF,mBAAO,CAAC,kBAAM,CAAC;AACzC,MAAMG,KAAK,GAAGH,mBAAO,CAAC,wCAAiB,CAAC;AACxC,MAAM;EAAEI;AAAY,CAAC,GAAGJ,mBAAO,CAAC,0CAAkB,CAAC;AACnD,MAAMK,iBAAiB,GAAGL,mBAAO,CAAC,oDAAuB,CAAC;;AAE1D;AACA,IAAIM,UAAU;;AAEd;AACA,IAAIH,KAAK,EAAE;EACTH,mBAAO,CAAC,wCAAiB,CAAC,CAACO,SAAS,EAAE;IACpCC,QAAQ,EAAEN,OAAO,CAACK,SAAS,EAAE,IAAI,EAAE,cAAc,EAAE,MAAM,EAAE,UAAU,CAAC;IACtEE,eAAe,EAAE;EACnB,CAAC,CAAC;AACJ;AAEA,SAASC,YAAYA,CAAA,EAAG;EACtB;EACA,MAAMC,eAAe,GAAGN,iBAAiB,CAAC;IACxCO,YAAY,EAAE,IAAI;IAClBC,aAAa,EAAE;EACjB,CAAC,CAAC;;EAEF;EACAP,UAAU,GAAG,IAAIX,aAAa,CAAC;IAC7BmB,CAAC,EAAEH,eAAe,CAACG,CAAC;IACpBC,CAAC,EAAEJ,eAAe,CAACI,CAAC;IACpBC,KAAK,EAAEL,eAAe,CAACK,KAAK;IAC5BC,MAAM,EAAEN,eAAe,CAACM,MAAM;IAC9BC,QAAQ,EAAE,GAAG;IACbC,SAAS,EAAE,GAAG;IACdC,IAAI,EAAE,KAAK;IAAE;IACbC,IAAI,EAAEpB,IAAI,CAACM,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,UAAU,CAAC;IAAE;IACzDe,cAAc,EAAE;MACdC,eAAe,EAAE,KAAK;MAAE;MACxBC,gBAAgB,EAAE,IAAI;MAAE;MACxBC,kBAAkB,EAAE,KAAK;MAAE;MAC3BC,OAAO,EAAEzB,IAAI,CAACM,SAAS,EAAE,IAAI,EAAE,SAAS,EAAE,YAAY,CAAC;MAAE;MACzDoB,WAAW,EAAE,IAAI;MAAE;MACnBC,2BAA2B,EAAE,KAAK;MAAE;MACpCC,oBAAoB,EAAE,KAAK,CAAC;IAC9B;EACF,CAAC,CAAC;;EAEF;EACAlB,eAAe,CAACmB,MAAM,CAACxB,UAAU,CAAC;;EAElC;EACA,MAAMyB,QAAQ,GAAG5B,KAAK,GAClB,uBAAuB,GACvB,UAAUF,IAAI,CAACM,SAAS,EAAE,wBAAwB,CAAC,EAAE;EAEzDD,UAAU,CAAC0B,OAAO,CAACD,QAAQ,CAAC;;EAE5B;EACAzB,UAAU,CAAC2B,IAAI,CAAC,eAAe,EAAE,MAAM;IACrC3B,UAAU,CAACc,IAAI,CAAC,CAAC;;IAEjB;IACA,IAAIjB,KAAK,EAAE;MACTG,UAAU,CAAC4B,WAAW,CAACC,YAAY,CAAC,CAAC;IACvC;EACF,CAAC,CAAC;;EAEF;EACA7B,UAAU,CAAC8B,EAAE,CAAC,QAAQ,EAAE,MAAM;IAC5B9B,UAAU,GAAG,IAAI;EACnB,CAAC,CAAC;;EAEF;EACAA,UAAU,CAAC4B,WAAW,CAACG,oBAAoB,CAAC,CAAC;IAAEC;EAAI,CAAC,KAAK;IACvDzC,KAAK,CAAC0C,YAAY,CAACD,GAAG,CAAC;IACvB,OAAO;MAAEE,MAAM,EAAE;IAAO,CAAC;EAC3B,CAAC,CAAC;AACJ;;AAEA;AACA9C,GAAG,CAAC+C,SAAS,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;EACzBhC,YAAY,CAAC,CAAC;;EAEd;EACAhB,GAAG,CAAC0C,EAAE,CAAC,UAAU,EAAE,MAAM;IACvB,IAAIzC,aAAa,CAACgD,aAAa,CAAC,CAAC,CAACC,MAAM,KAAK,CAAC,EAAE;MAC9ClC,YAAY,CAAC,CAAC;IAChB;EACF,CAAC,CAAC;AACJ,CAAC,CAAC;;AAEF;AACAhB,GAAG,CAAC0C,EAAE,CAAC,mBAAmB,EAAE,MAAM;EAChC;EACA,IAAIS,OAAO,CAACC,QAAQ,KAAK,QAAQ,EAAE;IACjCpD,GAAG,CAACqD,IAAI,CAAC,CAAC;EACZ;AACF,CAAC,CAAC;;AAEF;AACArD,GAAG,CAAC0C,EAAE,CAAC,sBAAsB,EAAE,CAACY,KAAK,EAAEC,QAAQ,KAAK;EAClDA,QAAQ,CAACb,EAAE,CAAC,YAAY,EAAE,CAACY,KAAK,EAAEE,aAAa,KAAK;IAClDF,KAAK,CAACG,cAAc,CAAC,CAAC;IACtBtD,KAAK,CAAC0C,YAAY,CAACW,aAAa,CAAC;EACnC,CAAC,CAAC;AACJ,CAAC,CAAC;;AAEF;AACApD,OAAO,CAACsD,MAAM,CAAC,aAAa,EAAE,MAAM;EAClC,OAAO1D,GAAG,CAAC2D,UAAU,CAAC,CAAC;AACzB,CAAC,CAAC;AAEFvD,OAAO,CAACsD,MAAM,CAAC,kBAAkB,EAAE,OAAOJ,KAAK,EAAEM,OAAO,KAAK;EAC3D,MAAMC,MAAM,GAAG,MAAMxD,MAAM,CAACyD,cAAc,CAAClD,UAAU,EAAEgD,OAAO,CAAC;EAC/D,OAAOC,MAAM;AACf,CAAC,CAAC;;AAEF;AACA,IAAI,CAACpD,KAAK,EAAE;EACVC,WAAW,CAACqD,wBAAwB,CAAC,CAAC;EAEtCrD,WAAW,CAACgC,EAAE,CAAC,kBAAkB,EAAE,MAAM;IACvCrC,MAAM,CAACyD,cAAc,CAAClD,UAAU,EAAE;MAChCoD,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,kBAAkB;MACzBC,OAAO,EAAE,sEAAsE;MAC/EC,OAAO,EAAE,CAAC,IAAI;IAChB,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFzD,WAAW,CAACgC,EAAE,CAAC,mBAAmB,EAAE,MAAM;IACxCrC,MAAM,CAACyD,cAAc,CAAClD,UAAU,EAAE;MAChCoD,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,cAAc;MACrBC,OAAO,EAAE,sEAAsE;MAC/EC,OAAO,EAAE,CAAC,SAAS,EAAE,OAAO;IAC9B,CAAC,CAAC,CAACnB,IAAI,CAAEa,MAAM,IAAK;MAClB,IAAIA,MAAM,CAACO,QAAQ,KAAK,CAAC,EAAE;QACzB1D,WAAW,CAAC2D,cAAc,CAAC,CAAC;MAC9B;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,C", "sources": ["webpack://risk-app/external commonjs \"electron\"", "webpack://risk-app/external commonjs \"electron-is-dev\"", "webpack://risk-app/external commonjs \"electron-reload\"", "webpack://risk-app/external commonjs \"electron-updater\"", "webpack://risk-app/external commonjs \"electron-window-state\"", "webpack://risk-app/external node-commonjs \"path\"", "webpack://risk-app/webpack/bootstrap", "webpack://risk-app/./electron/main/main.js"], "sourcesContent": ["module.exports = require(\"electron\");", "module.exports = require(\"electron-is-dev\");", "module.exports = require(\"electron-reload\");", "module.exports = require(\"electron-updater\");", "module.exports = require(\"electron-window-state\");", "module.exports = require(\"path\");", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "const { app, BrowserWindow, Menu, shell, ipcMain, dialog } = require('electron');\nconst { join, resolve } = require('path');\nconst isDev = require('electron-is-dev');\nconst { autoUpdater } = require('electron-updater');\nconst windowStateKeeper = require('electron-window-state');\n\n// Keep a global reference of the window object\nlet mainWindow;\n\n// Enable live reload for Electron in development\nif (isDev) {\n  require('electron-reload')(__dirname, {\n    electron: resolve(__dirname, '..', 'node_modules', '.bin', 'electron'),\n    hardResetMethod: 'exit'\n  });\n}\n\nfunction createWindow() {\n  // Load the previous window state or set defaults\n  const mainWindowState = windowStateKeeper({\n    defaultWidth: 1200,\n    defaultHeight: 800\n  });\n\n  // Create the browser window with security best practices\n  mainWindow = new BrowserWindow({\n    x: mainWindowState.x,\n    y: mainWindowState.y,\n    width: mainWindowState.width,\n    height: mainWindowState.height,\n    minWidth: 800,\n    minHeight: 600,\n    show: false, // Don't show until ready\n    icon: join(__dirname, '..', '..', 'public', 'icon.png'), // App icon\n    webPreferences: {\n      nodeIntegration: false, // Security: disable node integration\n      contextIsolation: true, // Security: enable context isolation\n      enableRemoteModule: false, // Security: disable remote module\n      preload: join(__dirname, '..', 'preload', 'preload.js'), // Preload script\n      webSecurity: true, // Security: enable web security\n      allowRunningInsecureContent: false, // Security: don't allow insecure content\n      experimentalFeatures: false // Security: disable experimental features\n    }\n  });\n\n  // Let windowStateKeeper manage the window\n  mainWindowState.manage(mainWindow);\n\n  // Load the React app\n  const startUrl = isDev\n    ? 'http://localhost:3000'\n    : `file://${join(__dirname, '../../build/index.html')}`;\n\n  mainWindow.loadURL(startUrl);\n\n  // Show window when ready to prevent visual flash\n  mainWindow.once('ready-to-show', () => {\n    mainWindow.show();\n\n    // Focus on window (in case it's hidden behind other windows)\n    if (isDev) {\n      mainWindow.webContents.openDevTools();\n    }\n  });\n\n  // Handle window closed\n  mainWindow.on('closed', () => {\n    mainWindow = null;\n  });\n\n  // Handle external links\n  mainWindow.webContents.setWindowOpenHandler(({ url }) => {\n    shell.openExternal(url);\n    return { action: 'deny' };\n  });\n}\n\n// App event handlers\napp.whenReady().then(() => {\n  createWindow();\n\n  // On macOS, re-create window when dock icon is clicked\n  app.on('activate', () => {\n    if (BrowserWindow.getAllWindows().length === 0) {\n      createWindow();\n    }\n  });\n});\n\n// Quit when all windows are closed\napp.on('window-all-closed', () => {\n  // On macOS, keep app running even when all windows are closed\n  if (process.platform !== 'darwin') {\n    app.quit();\n  }\n});\n\n// Security: Prevent new window creation\napp.on('web-contents-created', (event, contents) => {\n  contents.on('new-window', (event, navigationUrl) => {\n    event.preventDefault();\n    shell.openExternal(navigationUrl);\n  });\n});\n\n// IPC handlers for future database operations\nipcMain.handle('app-version', () => {\n  return app.getVersion();\n});\n\nipcMain.handle('show-message-box', async (event, options) => {\n  const result = await dialog.showMessageBox(mainWindow, options);\n  return result;\n});\n\n// Auto-updater events (for production)\nif (!isDev) {\n  autoUpdater.checkForUpdatesAndNotify();\n\n  autoUpdater.on('update-available', () => {\n    dialog.showMessageBox(mainWindow, {\n      type: 'info',\n      title: 'Update available',\n      message: 'A new version is available. It will be downloaded in the background.',\n      buttons: ['OK']\n    });\n  });\n\n  autoUpdater.on('update-downloaded', () => {\n    dialog.showMessageBox(mainWindow, {\n      type: 'info',\n      title: 'Update ready',\n      message: 'Update downloaded. The application will restart to apply the update.',\n      buttons: ['Restart', 'Later']\n    }).then((result) => {\n      if (result.response === 0) {\n        autoUpdater.quitAndInstall();\n      }\n    });\n  });\n}\n"], "names": ["app", "BrowserWindow", "<PERSON><PERSON>", "shell", "ipcMain", "dialog", "require", "join", "resolve", "isDev", "autoUpdater", "windowState<PERSON>eeper", "mainWindow", "__dirname", "electron", "hardResetMethod", "createWindow", "mainWindowState", "defaultWidth", "defaultHeight", "x", "y", "width", "height", "min<PERSON><PERSON><PERSON>", "minHeight", "show", "icon", "webPreferences", "nodeIntegration", "contextIsolation", "enableRemoteModule", "preload", "webSecurity", "allowRunningInsecureContent", "experimentalFeatures", "manage", "startUrl", "loadURL", "once", "webContents", "openDevTools", "on", "setWindowOpenHandler", "url", "openExternal", "action", "when<PERSON><PERSON><PERSON>", "then", "getAllWindows", "length", "process", "platform", "quit", "event", "contents", "navigationUrl", "preventDefault", "handle", "getVersion", "options", "result", "showMessageBox", "checkForUpdatesAndNotify", "type", "title", "message", "buttons", "response", "quitAndInstall"], "sourceRoot": ""}